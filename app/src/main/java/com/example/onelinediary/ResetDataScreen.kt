package com.example.onelinediary

import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.onelinediary.components.standardButtonColors
import com.example.onelinediary.components.standardButtonModifier
import com.example.onelinediary.components.standardButtonPadding
import com.example.onelinediary.components.standardButtonShape
import com.example.onelinediary.components.backButtonModifier

@Composable
fun ResetDataScreen(
    onBack: () -> Unit,
    saveDateManager: SaveDateManager
) {
    val context = LocalContext.current
    val backgroundColor = MaterialTheme.colorScheme.background
    
    // State for confirmation dialogs
    var showTodayConfirmDialog by remember { mutableStateOf(false) }
    var showAllConfirmDialog by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            // REMOVED .background(backgroundColor) to let main background show through
            .padding(16.dp),
        verticalArrangement = Arrangement.Top
    ) {
        // Top navigation row with back button
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Back button
            Button(
                onClick = onBack,
                modifier = backButtonModifier(),
                contentPadding = standardButtonPadding,
                colors = standardButtonColors(),
                shape = standardButtonShape
            ) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = "Back",
                    tint = MaterialTheme.colorScheme.onBackground
                )
            }

            // Title
            Text(
                text = "Reset Data",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )

            // Spacer to balance the layout
            Spacer(modifier = Modifier.width(48.dp))
        }

        Spacer(modifier = Modifier.height(32.dp))

        // Reset Today's Data option
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Text(
                    text = "Reset Today's Data",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "This will clear only today's entries. Past entries will be preserved.",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Button(
                    onClick = { showTodayConfirmDialog = true },
                    modifier = standardButtonModifier(Modifier.fillMaxWidth()),
                    contentPadding = standardButtonPadding,
                    colors = standardButtonColors(),
                    shape = standardButtonShape
                ) {
                    Text(
                        text = "Reset Today",
                        color = MaterialTheme.colorScheme.onBackground
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Reset All Data option
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Text(
                    text = "Reset All Data",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.error
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "This will clear all saved entries. This action cannot be undone.",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Button(
                    onClick = { showAllConfirmDialog = true },
                    modifier = standardButtonModifier(Modifier.fillMaxWidth()),
                    contentPadding = standardButtonPadding,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error,
                        contentColor = MaterialTheme.colorScheme.onError
                    ),
                    shape = standardButtonShape
                ) {
                    Text(
                        text = "Reset All Data",
                        color = MaterialTheme.colorScheme.onError
                    )
                }
            }
        }
    }

    // Reset Today confirmation dialog
    if (showTodayConfirmDialog) {
        AlertDialog(
            onDismissRequest = { showTodayConfirmDialog = false },
            title = { Text("Reset Today's Data?") },
            text = { Text("This will clear only today's entries. Past entries will be preserved. This action cannot be undone.") },
            confirmButton = {
                Button(
                    onClick = {
                        val success = saveDateManager.clearTodayData()
                        showTodayConfirmDialog = false

                        val message = if (success) {
                            "Today's data has been reset"
                        } else {
                            "Reset partially completed. Some of today's files may remain."
                        }
                        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
                    },
                    modifier = standardButtonModifier(),
                    contentPadding = standardButtonPadding,
                    colors = standardButtonColors(),
                    shape = standardButtonShape
                ) {
                    Text("Reset", color = MaterialTheme.colorScheme.onBackground)
                }
            },
            dismissButton = {
                Button(
                    onClick = { showTodayConfirmDialog = false },
                    modifier = standardButtonModifier(),
                    contentPadding = standardButtonPadding,
                    colors = standardButtonColors(),
                    shape = standardButtonShape
                ) {
                    Text("Cancel", color = MaterialTheme.colorScheme.onBackground)
                }
            }
        )
    }

    // Reset All confirmation dialog
    if (showAllConfirmDialog) {
        AlertDialog(
            onDismissRequest = { showAllConfirmDialog = false },
            title = { Text("Reset All Data?") },
            text = { Text("This will clear all saved entries. This action cannot be undone.") },
            confirmButton = {
                Button(
                    onClick = {
                        val success = saveDateManager.clearAllData()
                        showAllConfirmDialog = false
                        val message = if (success) {
                            "All data has been reset"
                        } else {
                            "Reset partially completed. Some files may remain."
                        }
                        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error,
                        contentColor = MaterialTheme.colorScheme.onError
                    )
                ) {
                    Text("Reset", color = MaterialTheme.colorScheme.onError)
                }
            },
            dismissButton = {
                Button(
                    onClick = { showAllConfirmDialog = false },
                    modifier = standardButtonModifier(),
                    contentPadding = standardButtonPadding,
                    colors = standardButtonColors(),
                    shape = standardButtonShape
                ) {
                    Text("Cancel", color = MaterialTheme.colorScheme.onBackground)
                }
            }
        )
    }
}
