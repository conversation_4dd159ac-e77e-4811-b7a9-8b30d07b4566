package com.example.onelinediary.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import com.example.onelinediary.ui.theme.DecorationTheme
import com.example.onelinediary.ui.theme.LocalAppTheme
import kotlin.math.cos
import kotlin.math.sin
import kotlin.random.Random

@Composable
fun BackgroundDecorations(
    decorationTheme: DecorationTheme,
    modifier: Modifier = Modifier
) {
    when (decorationTheme) {
        DecorationTheme.NONE, DecorationTheme.STANDARD -> {
            // No decorations
        }
        DecorationTheme.ROMANTIC -> {
            RomanticDecorations(modifier = modifier)
        }
    }
}

@Composable
fun RomanticDecorations(modifier: Modifier = Modifier) {
    val density = LocalDensity.current
    val primaryColor = MaterialTheme.colorScheme.primary
    val onBackgroundColor = MaterialTheme.colorScheme.onBackground
    
    Canvas(modifier = modifier.fillMaxSize()) {
        val canvasWidth = size.width
        val canvasHeight = size.height
        
        // Create a seeded random for consistent decoration placement
        val random = Random(42) // Fixed seed for consistent placement
        
        // Draw flowers
        repeat(8) { index ->
            val x = random.nextFloat() * canvasWidth
            val y = random.nextFloat() * canvasHeight
            val size = with(density) { (20 + random.nextFloat() * 15).dp.toPx() }
            val alpha = 0.4f + random.nextFloat() * 0.3f

            drawFlower(
                center = Offset(x, y),
                size = size,
                color = primaryColor.copy(alpha = alpha)
            )
        }

        // Draw hearts
        repeat(6) { index ->
            val x = random.nextFloat() * canvasWidth
            val y = random.nextFloat() * canvasHeight
            val size = with(density) { (16 + random.nextFloat() * 12).dp.toPx() }
            val alpha = 0.3f + random.nextFloat() * 0.3f

            drawHeart(
                center = Offset(x, y),
                size = size,
                color = onBackgroundColor.copy(alpha = alpha)
            )
        }
    }
}

fun DrawScope.drawFlower(
    center: Offset,
    size: Float,
    color: Color
) {
    val petalCount = 5
    val petalSize = size * 0.6f
    val angleStep = (2 * Math.PI / petalCount).toFloat()
    
    // Draw petals
    for (i in 0 until petalCount) {
        val angle = i * angleStep
        val petalCenter = Offset(
            center.x + cos(angle) * size * 0.3f,
            center.y + sin(angle) * size * 0.3f
        )
        
        drawCircle(
            color = color,
            radius = petalSize,
            center = petalCenter
        )
    }
    
    // Draw flower center
    drawCircle(
        color = color.copy(alpha = color.alpha * 1.5f),
        radius = size * 0.2f,
        center = center
    )
}

fun DrawScope.drawHeart(
    center: Offset,
    size: Float,
    color: Color
) {
    val path = Path()
    
    // Heart shape using bezier curves
    val heartWidth = size
    val heartHeight = size * 0.8f
    
    val startX = center.x
    val startY = center.y + heartHeight * 0.3f
    
    path.moveTo(startX, startY)
    
    // Left curve
    path.cubicTo(
        startX - heartWidth * 0.5f, startY - heartHeight * 0.5f,
        startX - heartWidth * 0.5f, startY - heartHeight * 0.8f,
        startX - heartWidth * 0.25f, startY - heartHeight * 0.6f
    )
    
    // Top left arc
    path.cubicTo(
        startX - heartWidth * 0.1f, startY - heartHeight * 0.8f,
        startX + heartWidth * 0.1f, startY - heartHeight * 0.8f,
        startX + heartWidth * 0.25f, startY - heartHeight * 0.6f
    )
    
    // Right curve
    path.cubicTo(
        startX + heartWidth * 0.5f, startY - heartHeight * 0.8f,
        startX + heartWidth * 0.5f, startY - heartHeight * 0.5f,
        startX, startY
    )
    
    path.close()
    
    drawPath(
        path = path,
        color = color
    )
}
